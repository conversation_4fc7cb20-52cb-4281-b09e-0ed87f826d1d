import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {
  const { token, password } = await request.json();

  if (!token || !password) {
    throw new Error('Token and new password are required.');
  }

  const user = await prisma.user.findFirst({
    where: {
      passwordResetExpires: { gt: new Date() }, // Token must not be expired
    },
  });

  if (!user) {
    throw new Error('Invalid or expired password reset token.');
  }

  // Verify the hashed token
  const isTokenValid = await bcrypt.compare(token, user.passwordResetToken || '');

  if (!isTokenValid) {
    throw new Error('Invalid or expired password reset token.');
  }

  const hashedPassword = await bcrypt.hash(password, 12);

  await prisma.user.update({
    where: { id: user.id },
    data: {
      password: hashedPassword,
      passwordResetToken: null,
      passwordResetExpires: null,
    },
  });

  return NextResponse.json({
    success: true,
    data: { message: 'Your password has been reset successfully.' }
  });
});